"use client";

import { IEvent } from "@/lib/database/models/event.model";
import { SignedIn, SignedOut, useUser } from "@clerk/nextjs";
import Link from "next/link";
import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import Checkout from "./Checkout";
import { Edit } from "lucide-react";

const CheckoutButton = ({ event }: { event: IEvent }) => {
  const { user } = useUser();
  const userId = user?.publicMetadata.userId as string;
  const email = user?.primaryEmailAddress?.emailAddress as string;
  const hasEventFinished = new Date(event.endDateTime) < new Date();

  return (
    <div className="flex items-center gap-3">
      {userId === event.organizer._id ? (
        <Button asChild className="button rounded-full" size="lg">
          <Link href={`/events/${event._id}/update`}>
            <Edit />
            Modify
          </Link>
        </Button>
      ) : hasEventFinished ? (
        <p className="p-2 text-red-400">
          Sorry, tickets are no longer available.
        </p>
      ) : (
        <>
          <SignedOut>
            <Button asChild className="button rounded-full" size="lg">
              <Link href="/sign-in">Get Ticket</Link>
            </Button>
          </SignedOut>

          <SignedIn>
            <Checkout event={event} userId={userId} email={email} />
          </SignedIn>
        </>
      )}
    </div>
  );
};

export default CheckoutButton;
