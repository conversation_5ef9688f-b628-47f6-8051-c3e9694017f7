import { Button } from "@/components/ui/button";
import Collection from "@/components/shared/Collection";
import Image from "next/image";
import Link from "next/link";
import { getAllEvents } from "@/lib/actions/event.actions";
import { SearchParamProps } from "@/types";
import Search from "@/components/shared/Search";
import CategoryFilter from "@/components/shared/CategoryFilter";

export default async function Home({ searchParams }: SearchParamProps) {
  const page = Number(searchParams?.page) || 1;
  const searchText = (searchParams?.query as string) || "";
  const category = (searchParams?.category as string) || "";

  const events = await getAllEvents({
    query: searchText,
    category: category,
    limit: 6,
    page: page,
  });
  return (
    <>
      <section className="bg-primary-50 bg-dotted-pattern bg-contain py-5 md:py-10">
        <div className="wrapper grid grid-cols-1 md:grid-cols-2 gap-5 md:gap-0">
          <div className="flex flex-col justify-center gap-8">
            <h1 className="h1-bold">
              Unlock Expert Knowledge: Anytime, Anywhere
            </h1>
            <p className="p-regular-20 md:p-regular-44">
              Book and learn helpful tips from 3168+ mentors in world-class
              companies with our global community.
            </p>
            <Button size="lg" asChild className="button w-full sm:w-fit">
              <Link href="#events">Explore Now</Link>
            </Button>
          </div>

          <Image
            src="/assets/images/hero.png"
            alt="hero"
            width={1000}
            height={1000}
            className="max-h-[70vh] object-contain object-center 2xl:max-h-[50vh]"
          />
        </div>
      </section>

      <section id="events" className="wrapper my-8 flex flex-col md:gap-12">
        <h2 className="h2-bold">
          Trust by <br /> Thousands of Events
        </h2>
        <div className="w-full flex flex-col gap-5 md:flex-row ">
          <Search placeholder="Search events by title..." />
          <CategoryFilter />
        </div>
        <Collection
          data={events?.data}
          emptyTitle={"No Upcoming Event"}
          emptyStateSubtext={"Come Back Later"}
          collectionType="All_Events"
          limit={0}
          page={page}
          totalPages={events?.totalPages}
        />
      </section>
    </>
  );
}
