{"name": "events-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.1.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^5.7.0", "@uploadthing/react": "^6.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.379.0", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "next": "^14.0.0", "query-string": "^9.1.0", "react": "^18", "react-datepicker": "^7.3.0", "react-dom": "^18", "react-hook-form": "^7.52.2", "stripe": "^17.7.0", "svix": "^1.25.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.12.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}